/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 资讯管理-资讯列表
 */

// 提审
export function auditCommit(data) {
    return requestV1.postJson(`${prefix}/informationscience/audit/commit`, data);
}

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/informationscience/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/informationscience/insert`, data)
}

// 更新真实阅读数、点赞数、分数数、收藏数
export function numberUpdate (data) {
    return requestV1.putJson(`${prefix}/informationscience/number/update`, data);
}

// 更新上下架状态
export function pushstatusUpdate (data) {
    return requestV1.putForm(`${prefix}/informationscience/pushstatus/update`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/informationscience/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/informationscience/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/informationscience/query/page`, data)
}

// 更新置顶状态
export function topstatusUpdate (data) {
    return requestV1.putForm(`${prefix}/informationscience/topstatus/update`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/informationscience/update`, data)
}

// 保存虚拟设置
export function virtualsetupSave (data) {
    return requestV1.putJson(`${prefix}/informationscience/virtualsetup/save`, data);
}

// 根据url解析文章
export function crawling (data) {
    return requestV1.get(`${prefix}/informationscience/crawling`, data)
}

// 根据url解析文章
export function textCode (data) {
  return requestV1.postJson(`${prefix}/informationscience/textcode`, data)
}

// 清除文字链
export function textcodeClear (data) {
  return requestV1.postJson(`${prefix}/informationscience/textcode/clear`, data)
}
