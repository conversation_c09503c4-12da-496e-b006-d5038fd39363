/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from "@/config/env";
const prefix = '/manage/api';
const prefix2 = '/export/api';

//删除广告主的领袋页面配置
export function deleteWxAuthRecordCard(data) {
    return requestV1.get(prefix + '/wxAuth/deleteWxAuthRecordCard', data);
}

//获取广告主的领袋页面配置
export function getWxAuthRecordCard(data) {
    return requestV1.get(prefix + '/wxAuth/getWxAuthRecordCard', data);
}

//添加广告主的领袋页面配置
export function addWxAuthRecordCard(data) {
    return requestV1.postJson(prefix + '/wxAuth/addWxAuthRecordCard', data);
}

//编辑广告主的领袋页面配置
export function editWxAuthRecordCard(data) {
    return requestV1.postJson(prefix + '/wxAuth/editWxAuthRecordCard', data);
}

//获取广告主主体配置欢迎文案
export function getWxAuthPoster(data) {
    return requestV1.postJson(prefix + '/wxAuth/getWxAuthPoster', data);
}

//企业号广告主配置欢迎文案
export function setWxAuthPoster(data) {
    return requestV1.postJson(prefix + '/wxAuth/setWxAuthPoster', data);
}


//上传企业号欢迎文案图片
const uploadImg = env.ctx + '/manage/api/wxAuth/uploadImg'
export { uploadImg }

//企业号用户绑定设备
export function bindWxAuthUserDevice(data) {
    return requestV1.postJson(prefix + '/wxAuth/bindWxAuthUserDevice', data);
}

//广告主企业绑定设备
export function bindWxAuthDevice(data) {
    return requestV1.postJson(prefix + '/wxAuth/bindWxAuthDevice', data);
}

//分页获取企业所有未绑定的设备
export function listNoBindWxAuthDevice(data) {
    return requestV1.postJson(prefix + '/wxAuth/listNoBindWxAuthDevice', data);
}

//分页获取企业所有已经绑定的设备
export function listBindWxAuthDevice(data) {
    return requestV1.postJson(prefix + '/wxAuth/listBindWxAuthDevice', data);
}

//查询广告表格查询所有数据
export function queryList(data) {
    return requestV1.postJson(prefix + '/wxAuth/queryList', data);
}

//查询广告表格分页数据
export function wxAuthQueryPage(data) {
    return requestV1.postJson(prefix + '/wxAuth/queryPage', data);
}

//删除消息推送文案
export function deleteWxAuthPoster(data) {
    return requestV1.deleteForm(prefix + '/wxAuth/deleteWxAuthPoster', data);
}

//编辑消息推送文案
export function editWxAuthPoster(data) {
    return requestV1.putJson(prefix + '/wxAuth/editWxAuthPoster', data);
}



// 公众号重复率查询 /export/api/v1/associate/query/repetition/rate/list
export function exportRepetitionRateList(data){
    return requestV1.postJson(prefix2 + '/v1/associate/query/repetition/rate/list', data);
}


// 初始票据 
export function getwxcpserver(data){
    return requestV1.get(prefix + '/wxCp/forceWxCpService', data);
}