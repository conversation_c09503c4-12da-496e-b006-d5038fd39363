/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 财务管理-待结算明细表
 */

// 列表查询
export function queryList(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/query/list`, data)
}

// 根据id查询单个
export function queryOne(data) {
  return requestV1.get(`${prefix}/financewithdrawal/query/one`, data)
}

// 分页列表查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/query/page`, data)
}

// 提现审核
export function audit(data) {
  return requestV1.putJson(`${prefix}/financewithdrawal/audit`, data)
}

// 重新发起交易（提现）
export function againWithdrawal(data) {
  return requestV1.putForm(`${prefix}/financewithdrawal/againWithdrawal`, data)
}

// 撤回
export function manualWithdraw(data) {
  return requestV1.putJson(`${prefix}/financewithdrawal/manual/withdraw`, data)
}

// 提现
export function financewithdrawalAppSave(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/app/save`, data)
}

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteJson(`${prefix}/financewithdrawal/delete/batch`, data)
}

// 批量审核
export function auditBatch(data) {
  return requestV1.putJson(`${prefix}/financewithdrawal/audit/batch`, data)
}

// 统计 
export function financewithdrawalGetStatistic(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/get/statistic`, data)
}

// 修改金额
export function updateMoney(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/update/incentive/fee/price`, data)
}

// 获取钱包
export function getWallet(data) {
  return requestV1.get(`${prefix}/mywallet/get/mywallet/by/userid`, data)
}

//  提现列表-添加批量修改税率。
export function financewithdrawalBatchCommissionRate(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/batch/commissionRate`, data)
}

// 批量更新提现数据 
export function financewithdrawalBatchUpdatePayPayChannelOtherElse(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/batch/update/payChannelOtherElse`, data)
}

// 根据多个任务id生成提现
export function insertWithdrawalByTaskids(data) {
  return requestV1.postForm(`${prefix}/financewithdrawal/insert/withdrawal/by/taskids`, data)
}

// 更新发起提现时间 
export function financewithdrawalBatchUpdateCreate(data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/batch/update/createtime`, data)
}
