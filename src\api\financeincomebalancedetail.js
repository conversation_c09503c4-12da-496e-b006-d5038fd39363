/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 财务管理-待结算明细表
 */

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/financeincomebalancedetail/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/financeincomebalancedetail/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/financeincomebalancedetail/query/page`, data)
}

// 待结算明细待结算、已结算余额统计
export function statBalance (data) {
    return requestV1.get(`${prefix}/financeincomebalancedetail/stat/balance`, data)
}
