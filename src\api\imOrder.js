import requestV1 from '@/common/utils/modules/request'

const prefix = '/im/api/v1'

/**
 * 坐席管理
 */

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/order/query/page`, data);
}

// 根据id查询
export function getById (data) {
    return requestV1.get(`${prefix}/order/get/by/id`, data);
}

// 列表查询
export function queryConsultList (data) {
    return requestV1.get(`${prefix}/order/query/consult/list`, data);
}
