/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 资讯管理-资讯分类
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/informationclassify/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/informationclassify/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/informationclassify/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/informationclassify/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/informationclassify/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/informationclassify/update`, data)
}
