/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import axios from 'axios'
import env from "@/config/env";
const prefix = '/manage/api'
import { getToken } from '@/utils/auth'

//进入公众号授权页面
export function gotoPreAuth(data) {
    return requestV1.get(prefix + '/open/gotoPreAuth', data);
}

// 进入御航公众号授权页面
export function yhGotoPreAuth(data) {
  return requestV1.get('/yh/api/open/gotoPreAuth', data);
}

// goupId 1000: 默认分组 2000：商品 3000：二维码 4000：推送 6000证件 7000 广告投放计划计划 8000用户图片

const doUpload = env.ctx + '/basics/api/v1/attachment/upload'

export { doUpload }


// 上传组件上传文件API
export function uploadFileAxios(data, params, onUploadProgress) {
    return requestV1.uploadFileAxios({
        url: doUpload,
        method: "post",
        data,
        params,
        onUploadProgress: onUploadProgress,
    }, '')
}

// 下载附件
const downLoad = env.ctx + '/basics/api/v1/attachment/download'
export { downLoad }

export function deleteByPath(data) {
    return requestV1.deleteForm(prefix + '/upload/deleteByPath', data);
}

// 导出查询到的记录
export function hospitalExportExcel() {
    return '/manage/api/hospital/exportExcel'
}

// excel导入数据
const uploadExcel = env.ctx + '/manage/api/hospital/uploadExcel'
const createtUrl = env.ctx + '/manage/api/wx/createWxRedirectUrl'
export { uploadExcel, createtUrl }

// 导出查询到的记录
export function memberCouponExportExcel() {
    return env.ctx + '/manage/api/memberCoupon/exportExcel'
}
export function loadDataPanel(data) {
    return requestV1.get(prefix + '/statistics/loadDataPanel', data);
}

// 添加医院信息
export function loadMachinesOutPackageInWeek(data) {
    return requestV1.postJson(prefix + '/hospital/add', data);
}

// 编辑医院信息
export function edit(data) {

    return requestV1.postJson(prefix + '/hospital/edit', data);
}

// 查询表格分页数据
export function pageVo(data) {
    return requestV1.postJson(prefix + '/hospital/queryPage', data);
}

// 添加标签
export function tagsAdd(data) {
    return requestV1.postJson(prefix + '/tags/add', data);
}

// 编辑标签
export function tagsEdit(data) {
    return requestV1.postJson(prefix + '/tags/edit', data);
}

// 搜索标签
export function queryPage(data) {
    return requestV1.postJson(prefix + '/tags/queryPage', data);
}

// 删除标签
export function tagDelete(data) {
    return requestV1.postJson(prefix + '/tags/delete', data);
}

// 查询医院列表数据
export function findById(data) {
    return requestV1.get(prefix + '/hospital/findById', data);
}

// 根据父节点id查询字典表
export function findByParentId(data) {
    return requestV1.get('/manage/api/code/findByParentId', data);
}

export function getCodeById(data) {
    return requestV1.get(prefix + '/code/getCodeById', data);
}

// 编辑字典数据
export function codeEdit(data) {
    return requestV1.postJson(prefix + '/code/edit', data);
}
// 删除字典数据
export function codeDelete(data) {
    return requestV1.postJson(prefix + '/code/delete', data);
}

// 添加字典数据
export function addCode(data) {
    return requestV1.postJson(prefix + '/code/add', data);
}

// 获取所有挂号网信息
export function listRegisterNet(data) {



    return requestV1.get(prefix + '/registerNet/listRegisterNet', data);
}

// 新增优惠券
export function couponAdd(data) {


    return requestV1.postJson(prefix + '/coupon/add', data);
}

// 查询所有医院数据
export function listAllHospital(data) {
    return requestV1.get(prefix + '/hospital/listAllHospital', data);
}

// 查询优惠券分页数据
export function couponQueryPage(data) {
    return requestV1.postJson('/manage/api/coupon/queryPage', data);
}

// 启用优惠券
export function startCoupon(data) {
    return requestV1.postJson('/manage/api/coupon/startCoupon', data);
}

// 停用优惠券
export function stopCoupon(data) {
    return requestV1.postJson('/manage/api/coupon/stopCoupon', data);
}

// 修改优惠券提示语
export function couponEdit(data) {
    return requestV1.postJson('/manage/api/coupon/edit', data);
}

// 查询领券记录分页数据
export function memberCoupon(data) {
    return requestV1.postJson('/manage/api/coupon/queryPage', data);
}

// 查询所有医院标签
export function listAllTags(data) {
    return requestV1.postJson(prefix + '/tags/listAllTags', data);
}

// 查询医院所有标签
export function getHospitalTags(data) {
    return requestV1.postJson(prefix + '/hospital/getHospitalTags', data);
}

// 查询医院所有标签
export function hospitalDelete(data) {
    return requestV1.postJson(prefix + '/hospital/delete', data);
}

// 获取公众号优惠券二维码
export function getCouponWxQrCode(data) {
    return requestV1.postJson('/manage/api/coupon/getCouponWxQrCode', data);
}

export function createWxRedirect(path) {
    return env.ctx + '/manage/api/wx/createWxRedirect?path=' + path
}

// 获取微信跳转二维码链接
export function createWxRedirectUrl(data) {
    return requestV1.get('/manage/api/wx/createWxRedirectUrl', data);
}

// 登录接口
export function login(data) {
    return requestV1.postJson(prefix + '/login/login', data);
}

// 
export function getCodeLikeId(data) {
    return requestV1.get('/manage/api/code/getCodeLikeId', data)
}

// 根据相对路径查询附件信息
export function attachmentQueryParam(data) {
    return requestV1.get('/basics/api/v1/attachment/query/param', data)
}

// 微信通用包装授权链接
export function wxGetCommonOauth2WrapUrl(data) {
    const instance = axios.create();
    instance.interceptors.response.use(res => { return res.data })
    return instance({
        method: 'GET',
        baseURL: `${env.ctx}/manage/api/wx/get/common/oauth2/wrap/url`,
        params: data,
        headers: { 'Authorization': getToken() }
    })
}

// 上传文件到oss
export function mediaUploadOssByUrl(data) {
    return requestV1.get('/basics/api/url/mediaUploadOssByUrl', data)
}
