import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api'

//获取部门所有员工，包含下级部门
export function listDepartmentAllStaff(data) {
  return requestV1.postForm(prefix + '/department/listDepartmentAllStaff', data);
}

//批量删除部门员工
export function deletesDepartmentStaff(data) {
  return requestV1.postJson(prefix + '/department/deletesDepartmentStaff', data);
}

//批量增加部门员工
export function batchAddDepartmentStaff(data) {
  return requestV1.postJson(prefix + '/department/batchAddDepartmentStaff', data);
}

//查询部门员工所有数据
export function queryListByStaff(data) {
  return requestV1.postJson(prefix + '/department/queryListByStaff', data);
}

//查询部门员工表格分页数据
export function queryPageByStaff(data) {
  return requestV1.postJson(prefix + '/department/queryPageByStaff', data);
}

//编辑部门信息
export function editDepartment(data) {
  return requestV1.postJson(prefix + '/department/editDepartment', data);
}

//添加部门
export function addDepartment(data) {
  return requestV1.postJson(prefix + '/department/addDepartment', data);
}

//查询所有部门名称数据
export function queryList(data) {
  return requestV1.postJson(prefix + '/department/queryList', data);
}

//批量删除部门信息
export function deletes(data) {
  return requestV1.postJson(prefix + '/department/deletes', data);
}

//查询表格分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix + '/department/queryPage', data);

}
