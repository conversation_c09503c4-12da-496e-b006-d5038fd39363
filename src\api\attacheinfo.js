/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 专员档案
 */

// 批量更改认证状态
export function batchUpdateStatus (data) {
    return requestV1.putJson(`${prefix}/attacheinfo/batchUpdateStatus`, data);
}

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/attacheinfo/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/attacheinfo/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/attacheinfo/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/attacheinfo/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/attacheinfo/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/attacheinfo/update`, data)
}

// 档案导入
export const upload = `${env.ctx + prefix}/attacheinfo/upload`
