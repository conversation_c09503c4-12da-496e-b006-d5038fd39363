/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//绑定租户
export function bindTenant(data) {
    return requestV1.postJson(prefix + '/allinPayMember/bindTenant', data);
}

//获取所有未绑定的会员
export function listNoBindMember(data) {
    return requestV1.get(prefix + '/operator/listNoBindMember', data);
}

//影印件采集
export function idCardCollect(data) {
    return requestV1.postJson(prefix + '/allinPayMember/idCardCollect', data);
}

//解绑绑银行卡
export function deleteMember(data) {
    return requestV1.deleteForm(prefix + '/allinPayMember/deleteMember', data);
}

//解绑绑银行卡
export function unbindPhone(data) {
    return requestV1.postJson(prefix + '/allinPayMember/unbindPhone', data);
}

//解绑绑银行卡
export function unBindBankCard(data) {
    return requestV1.postJson(prefix + '/allinPayMember/unBindBankCard', data);
}

//绑定银行卡
export function bindBankCard(data) {
    return requestV1.postJson(prefix + '/allinPayMember/bindBankCard', data);
}

//查询会员信息
export function getAllinPayMemberById(data) {
    return requestV1.postForm(prefix + '/allinPayMember/getAllinPayMemberById', data);
}

//通商云会员电子协议签约
export function memberSignContract(data) {
    return requestV1.postJson(prefix + '/allinPayMember/memberSignContract', data);
}

//会员电子余额协议签约
export function signBalanceProtoCol(data) {
    return requestV1.get(prefix + '/allinPayMember/signBalanceProtoCol', data);
}

//绑定手机
export function bindPhone(data) {
    return requestV1.postJson(prefix + '/allinPayMember/bindPhone', data);
}

//发送验证码
export function sendVerificationCode(data) {
    return requestV1.postJson(prefix + '/allinPayMember/sendVerificationCode', data);
}

//添加基本信息
export function addBaseInfo(data) {
    return requestV1.postJson(prefix + '/allinPayMember/addBaseInfo', data);
}

//创建会员
export function createMember(data) {
    return requestV1.postJson(prefix + '/allinPayMember/createMember', data);
}

//添加通联旧会员
export function addOldMember(data) {
    return requestV1.postJson(prefix + '/allinPayMember/addOldMember', data);
}

//查询所有通联会员
export function queryList(data) {
    return requestV1.postJson(prefix + '/allinPayMember/queryList', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/allinPayMember/queryPage', data);
}

// 消费申请
export function consumeApply(data) {
  return requestV1.postForm(prefix + '/allinPayMember/consumeApply', data);
}
