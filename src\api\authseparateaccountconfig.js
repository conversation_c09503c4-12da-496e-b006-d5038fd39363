/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from "@/config/env";

let prefix = '/manage/api/v1'

// excel导入数据
const uploadExcel = env.ctx + '/export/api/v1/separateaccountconfig/uploadexcel'
export { uploadExcel }

//引流号分账配置
export function authseparateaccountconfigQuery(data) {
    return requestV1.postJson(prefix + '/authseparateaccountconfig/query/page', data);
}
//引流号分账配置详情分页查询
export function authseparateaccountconfigDetailQuery(data) {
    return requestV1.postJson(prefix + '/authseparateaccountconfigdetail/query/page', data);
}
//引流号分账配置详情查询
export function authseparateaccountconfigDetailList(data) {
    return requestV1.get(prefix + '/authseparateaccountconfigdetail/query/list', data);
}
//引流号分账保留数据
export function authseparateaccountconfigInsert(data) {
    return requestV1.postJson(prefix + '/authseparateaccountconfig/insert', data);
}
//引流号分账更新数据
export function authseparateaccountconfigUpdate(data) {
    return requestV1.putJson(prefix + '/authseparateaccountconfig/update', data);
}