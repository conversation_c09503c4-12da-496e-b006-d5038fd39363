/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

//查询所有合作商列表
export function queryList(data) {
    return requestV1.get(prefix + '/businessinfo/query/list', data);
}

//查询合作商家账户分页数据
export function queryBusinessAccountPage(data) {
    return requestV1.postJson(prefix + '/businessinfo/query/businessAccountPage', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/businessinfo/query/page', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/businessinfo/insert', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/businessinfo/query/one', data);
}

//绑定用户
export function updateFn(data) {
    return requestV1.putJson(prefix + '/businessinfo/update', data);
}

//测试服务回调地址
export function testCallbackUrl(data) {
    return requestV1.get(prefix + '/businessinfo/testCallbackUrl', data);
}

//获取未绑定（自身已绑定包含）用户列表
export function queryUnBindUser(data) {
    return requestV1.get(prefix + '/businessinfo/queryUnBindUser', data);
}

//获取合作商端首页数据统计
export function indexStatistics(data) {
    return requestV1.get(prefix + '/businessinfo/indexStatistics', data);
}

//获取合作商端首页粉丝统计
export function siteFansList(data) {
    return requestV1.get(prefix + '/businessinfo/siteFansList', data);
}

//合作商端--获取合作商端基础信息
export function getBusinessinfo(data) {
    return requestV1.get(prefix + '/businessinfo/getBusinessinfo', data);
}

//查询合作商家自己账户数据
export function queryOwnAccount(data) {
    return requestV1.get(prefix + '/businessinfo/query/queryOwnAccount', data);
}