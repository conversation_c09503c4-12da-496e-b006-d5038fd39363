/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 任务奖励
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/incentivefee/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/incentivefee/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/incentivefee/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/incentivefee/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/incentivefee/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/incentivefee/update`, data)
}
