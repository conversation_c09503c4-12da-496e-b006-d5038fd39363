import requestV1 from '@/common/utils/modules/request'

const prefix = "/manage/api/v1"

/**
 * 支付宝活动投放计划
 * 
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/alipayseparateaccountconfig/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/alipayseparateaccountconfig/query/list`, data)
}

// 根据主键单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/alipayseparateaccountconfig/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/alipayseparateaccountconfig/query/page`, data)
}

// 
export function getSeparateaccountconfigByPutplanid (data) {
    return requestV1.get(`${prefix}/alipayseparateaccountconfig/get/separateaccountconfig/by/putplanid`, data)
}
