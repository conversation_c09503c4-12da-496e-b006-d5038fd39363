/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

//合作商收支明细分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/incomebalancedetail/query/page', data);
}

//合作商收支明细列表合计
export function queryDetailTotal(data) {
    return requestV1.postJson(prefix + '/incomebalancedetail/query/detailTotal', data);
}

//合作商收入日报表分页列表查询
export function queryDatePage(data) {
    return requestV1.postJson(prefix + '/incomebalancedetail/query/datePage', data);
}

//合作商收入日报列表合计
export function queryDateTotal(data) {
    return requestV1.postJson(prefix + '/incomebalancedetail/query/dateTotal', data);
}