/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 横幅管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/banner/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/banner/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/banner/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/banner/query/one`, data)
}

// 分页列表查询代办模板
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/banner/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/banner/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/banner/update`, data)
}

// 修改状态
export function updateOpenStatus (data) {
    return requestV1.postForm(`${prefix}/banner/update/open/status`, data)
}

// 查找横幅信息根据业务id和业务类型
export function findBannerByBusinesstypeAndBusinessid (data) {
    return requestV1.get(`${prefix}/banner/find/banner/by/businesstype/and/businessid`, data)
}