/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api'

// 根据安装单位ID获取标签列表
export function listInstallTagByInstallId(data) {
    return requestV1.get(prefix + '/installUnit/listInstallTagByInstallId', data);
}

// 获取所有备案机构
export function listAllByInstallType(data) {
    return requestV1.postJson(prefix + '/installUnit/listAllByInstallType', data);
}
// 获取经销商关联的所有安装单位
export function listAllOperatorInstallId(data) {
    return requestV1.postJson(prefix + '/installUnit/listAllOperatorInstallId', data);
}

//新增安装单位详情
export function addDetail(data) {
    return requestV1.postJson(prefix + '/installUnit/addDetail', data);
}

//编辑安装单位
export function edit(data) {
    return requestV1.postJson(prefix + '/installUnit/edit', data);
}

//删除安装单位
export function deleteUnit(data) {
    return requestV1.postJson(prefix + '/installUnit/delete', data);
}

//新增安装单位
export function add(data) {
    return requestV1.postJson(prefix + '/installUnit/add', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/installUnit/queryPage', data);
}

//根据安装单位类型获取安装单位信息
export function listByInstallType(data) {
    return requestV1.postJson(prefix + '/installUnit/listByInstallType', data);
}

export function findById(data) {
    return requestV1.postForm(prefix + '/installUnit/findById', data);
}
export function addOrUpdateInstallOperationType(data) {
    return requestV1.postJson(prefix + '/installUnit/addOrUpdateInstallOperationType', data);

}
