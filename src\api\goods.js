/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//上架商品列表
export function listGoodsByNormal(data) {
  return requestV1.get(prefix + '/goods/listGoodsByNormal', data);
}

//商品上下架
export function editGoodStatus(data) {
  return requestV1.putForm(prefix + '/goods/editGoodStatus', data);
}

//上传商品图片
export function uploadImg(data) {
  return requestV1.postJson(prefix + '/goods/uploadImg', data);
}

//编辑商品
export function edit(data) {
  return requestV1.putJson(prefix + '/goods/edit', data);
}

//删除商品数据列表
export function deleteGoods(data) {
  return requestV1.postJson(prefix + '/goods/delete', data);
}

//添加商品
export function add(data) {
  return requestV1.postJson(prefix + '/goods/add', data);
}

//查询表格分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix + '/goods/queryPage', data);
}
