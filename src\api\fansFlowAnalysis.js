/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//自营公众号累计粉丝数据
export function getSelfSupportFansData(data) {
    return requestV1.postJson(prefix + '/fansFlowAnalysis/getSelfSupportFansData', data);
}

//自营公众号用户量和收入
export function getSelfSupportFansAndIocome(data) {
    return requestV1.postJson(prefix + '/fansFlowAnalysis/getSelfSupportFansAndIocome', data);
}

//粉丝销售数据
export function getFansSalesData(data) {
    return requestV1.postJson(prefix + '/fansFlowAnalysis/getFansSalesData', data);
}

//粉丝销售单价分析
export function getFansUnitPriceAnalysis(data) {
    return requestV1.postJson(prefix + '/fansFlowAnalysis/getFansUnitPriceAnalysis', data);
}

//卖粉出库占比分析
export function getSellFansRate(data) {
    return requestV1.get(prefix + '/fansFlowAnalysis/getSellFansRate', data);
}

//粉丝地区占比情况
export function getFansAreaRate(data) {
    return requestV1.get(prefix + '/fansFlowAnalysis/getFansAreaRate', data);
}

//大小号分布情况
export function getBigSmallNumberBigCount(data) {
    return requestV1.get(prefix + '/fansFlowAnalysis/getBigSmallNumberBigCount', data);
}
