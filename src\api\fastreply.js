import requestV1 from '@/common/utils/modules/request'

const prefix = '/im/api/v1'

/**
 * 快捷回复
 */

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/fastreply/query/page`, data);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/fastreply/insert`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/fastreply/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/fastreply/query/one`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/fastreply/update`, data)
}

// 删除数据
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/fastreply/delete/one/${data.id}`)
}

// 根据类型获取快捷回复列表
export function findListByType (data) {
    return requestV1.postJson(`${prefix}/fastreply/find/list/by/type`, data)
}
