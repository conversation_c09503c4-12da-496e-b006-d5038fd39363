/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//新增h5页面配置
export function add(data) {
    return requestV1.postJson(prefix + '/h5Config/add', data);
}

//注销h5页面配置
export function deletes(data) {
    return requestV1.postJson(prefix + '/h5Config/deletes', data);
}

//编辑h5页面配置
export function edit(data) {
    return requestV1.postJson(prefix + '/h5Config/edit', data);
}


//根据ID获取h5页面配置详情
export function findById(data) {
    return requestV1.get(prefix + '/h5Config/findById', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/h5Config/queryPage', data);
}
