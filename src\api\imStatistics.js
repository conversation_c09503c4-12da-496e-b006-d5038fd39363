import requestV1 from '@/common/utils/modules/request'

const prefix = '/im/api/v1'

/**
 * im 数据分析管理
 */

// 查询订单日报表统计分页
export function queryOrderDailyPage (data) {
    return requestV1.postJson(`${prefix}/statistics/query/order/daily/page`, data);
}

// 查询订单日报统计汇总
export function queryOrderDailyTotal (data) {
    return requestV1.postJson(`${prefix}/statistics/query/order/daily/total`, data);
}

// 查询订单路径分析统计分页
export function queryOrderPathAnalysisPage (data) {
    return requestV1.postJson(`${prefix}/statistics/query/order/path/analysis/page`, data);
}

// 资讯订单会话统计分页列表查询
export function ordersessionstatQueryPage (data) {
    return requestV1.postJson(`${prefix}/statistics/ordersessionstat/query/page`, data)
}
