/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api'
const managePrefix = '/manage/api'


//批量注销机构备案
export function deletes(data) {
    return requestV1.postJson(prefix + '/installReport/deletes', data);
}

//释放机构备案
export function unlocks(data) {
    return requestV1.postJson(prefix + '/installReport/unlocks', data);
}

//导出数据
export function exportData(data) {
    return '/export/api/installReport/exportData'
}

//根据安装单位获取机构备案记录列表
export function queryInstallReportRecordPage(data) {
    return requestV1.postJson(prefix + '/installReport/queryInstallReportRecordPage', data);
}

//编辑机构备案
export function edit(data) {
    return requestV1.postJson(prefix + '/installReport/edit', data);
}

//新增机构备案
export function add(data) {
    return requestV1.postJson(prefix + '/installReport/add', data);
}

//查询表格分页数据
export function queryInstallReportPage(data) {
    return requestV1.postJson(managePrefix + '/installUnit/queryInstallReportPage', data);
}
