/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 资讯管理-资讯审核
 */

// 查看资讯审核记录
export function auditQuery(data) {
    return requestV1.get(`${prefix}/informationscienceauditlog/audit/query`, data);
}

// 保存资讯审核
export function auditSave(data) {
    return requestV1.postJson(`${prefix}/informationscienceauditlog/audit/save`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/informationscienceauditlog/query/list`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/informationscienceauditlog/query/page`, data)
}
