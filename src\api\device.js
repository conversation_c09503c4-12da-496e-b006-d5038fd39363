import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//批量设置设备检查状态
export function settingCheckStatus(data) {
  return requestV1.postJson(prefix + '/device/settingCheckStatus', data);
}

//主动下发检测设备状态(不用传checkStatus)
export function sendSynDevice(data) {
  return requestV1.postJson(prefix + '/device/sendSynDevice', data);
}

//根据设备ID获取标签列表
export function listDeviceTagByDeviceId(data) {
  return requestV1.get(prefix + '/device/listDeviceTagByDeviceId', data);
}

//设备批量切换是否开启小号投放
export function batchSwitchOpenAdvert(data) {
  return requestV1.postJson(prefix + '/device/batchSwitchOpenAdvert', data);
}

//批量绑定推送类型
export function batchSendType(data) {
  return requestV1.postJson(prefix + '/device/batchSendType', data);
}

//设备广告主拷贝
export function copyDeviceFansPlan(data) {
  return requestV1.postJson(prefix + '/device/copyDeviceFansPlan', data);
}

//测试出袋
export function getDeviceInfoByDeviceId(data) {
  return requestV1.get(prefix + '/device/getDeviceInfoByDeviceId', data);
}

//导出设备excel
export function exportData() {
  return '/export/api/device/exportExcel'
}
//测试出袋
export function testOutPacket(data) {
  return requestV1.get(prefix + '/device/testOutPacket', data);
}

//设备批量切换状态
export function deleteDevices(data) {
  return requestV1.postJson(prefix + '/device/deleteDevices', data);
}

//设备批量切换状态
export function batchSwitchStatus(data) {
  return requestV1.postJson(prefix + '/device/batchSwitchStatus', data);
}

//生成二维码
export function batchSwitchQrCodeType(data) {
  return requestV1.postJson(prefix + '/device/batchSwitchQrCodeType', data);
}

//生成二维码
export function getQrCode(data) {
  return requestV1.get(prefix + '/device/getQrCode', data);
}
//设备广告主排序
export function sortDeviceWxAuth(data) {
  return requestV1.postJson(prefix + '/wxAuth/sortDeviceWxAuth', data);
}

//获取设备投放的所有广告主
export function listDeviceAllAdvert(data) {
  return requestV1.postJson(prefix + '/device/listDeviceAllAdvert', data);
}

//编辑设备信息
export function editDevice(data) {
  return requestV1.postJson(prefix + '/device/editDevice', data);
}

//添加设备
export function addDevice(data) {
  return requestV1.postJson(prefix + '/device/addDevice', data);
}
//查询表格分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix + '/device/queryPage', data);
}

//批量切换经销商
export function batchSwitchOperator(data) {
  return requestV1.postJson(prefix + '/device/batchSwitchOperator', data)
}

// 设备批量切换领袋授权类型
export function deviceBatchSwitchPacketAuthType(data) {
  return requestV1.postJson(prefix + '/device/batchSwitchPacketAuthType', data)
}

// 批量切换支付宝免费按钮开关
export function batchUpdateAlipayInsuranceSwitchStatus(data) {
  return requestV1.postForm(prefix + '/device/batchUpdateAlipayInsuranceSwitchStatus', data)
}

// 获取设备实况面板
export function queryDeviceSituation(data) {
  return requestV1.get('/manage/api/device/queryDeviceSituation', data)
}

// 批量踢下线 /api/client/kickClientBatch
export function clientKickClientBatch(data) {
  return requestV1.putForm('/iotobm/api/client/kickClientBatch', data)
}

// 设备管理添加系统设备
// 
export function deviceaddDeviceBatch086(data) {
  return requestV1.get('/manage/api/device/addDeviceBatch086', data)
}


// 批量系统自绑
// 
export function batchAdClientIdBindDeviceClientId(data) {
  return requestV1.postJson('/manage/api/deviceSetup/batchAdClientIdBindDeviceClientId', data)
}




export function bathSetOperatorAndInstall(data) {
  return requestV1.postJson('/manage/api/device/bathSetOperatorAndInstall', data)

}

// 批量添加订阅通知
export function batchUpdateOpenSubscribeStatus(data) {
  return requestV1.postForm('/manage/api/device/batchUpdateOpenSubscribeStatus', data)
}

// 批量锁扫码入口禁用
export function batchDeviceLockdown(data) {
  return requestV1.postForm('/manage/api/device/batch/lockdown', data)
}

// 设备批量设置袋子类型
export function batchDeviceUpdatePacketType(data) {
  return requestV1.postForm('/manage/api/device/batch/update/packetType', data)
}

// 设备批量设置引流号类型
export function batchUpdateAuthTypes(data) {
  return requestV1.postForm(prefix + '/device/batch/update/authTypes',data)
}

// 设备批量设置推送权重
export function batchUpdatePushWeight(data) {
  return requestV1.postForm(prefix + '/device/batch/update/pushWeight',data)
}

// 批量设置落地类型 
export function batchUpdateLandingType(data) {
  return requestV1.postForm(prefix + '/device/batch/update/landingType',data)
}

// 设备批量设置引流号拓展内控
export function batchUpdateAuthTypesExt(data) {
  return requestV1.postForm(prefix + '/device/batch/update/authTypesExt',data)
}

// 设备批量设置手机授权开关
export function batchUpdatePhoneAuth(data) {
  return requestV1.postForm(prefix + '/device/batch/update/phoneAuth',data)
}

// 设备批量设置服务号永久订阅通知
export function batchUpdatePermanentSubscribe(data) {
  return requestV1.postForm(prefix + '/device/batch/update/permanentSubscribe',data)
}

// 批量设置广点通投放
export function batchUpdateGdtAdSwitch(data) {
  return requestV1.postForm(prefix + '/device/batch/update/gdtAdSwitch',data)
}
