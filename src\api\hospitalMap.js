/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'
const dmfix = '/dm/api/v1'
//按地区类型获取经销商
export function countOperatorByArea(data) {
     return requestV1.get(prefix + '/hospitalMap/countOperatorByArea', data);
}

//按地区类型获取经销商
export function listOperatorByType(data) {
    return requestV1.postJson(prefix + '/hospitalMap/listOperatorByType', data);
}

//按医院等级统计开拓数
export function countHospitalSettlementByLevel(data) {
    return requestV1.postJson(prefix + '/hospitalMap/countHospitalSettlementByLevel', data);
}

//按医院等级统计出袋量
export function countPacketByHospitalLevel(data) {
    return requestV1.postJson(prefix + '/hospitalMap/countPacketByHospitalLevel', data);
}


//按医院类别统计出袋量
export function countPacketByHospitalByType(data) {
    return requestV1.postJson(prefix + '/hospitalMap/countPacketByHospitalByType', data);
}


//按地区统计医院数量
export function cloutHospitalByArea(data) {
    return requestV1.postJson(prefix + '/hospitalMap/cloutHospitalByArea', data);
}

//按医院等级统计医院的设备状态
export function countDeviceStatusByLevel(data) {
   return requestV1.postJson(prefix + '/hospitalMap/countDeviceStatusByLevel', data);
}

//按医院类型统计医院的设备状态
export function countDeviceStatusByHospitalType(data) {
    return requestV1.postJson(prefix + '/hospitalMap/countDeviceStatusByHospitalType', data);
}

//统计总出袋数，同比数，环比数
export function countPacketByTime(data) {
    return requestV1.postJson(prefix + '/hospitalMap/countPacketByTime', data);
}

//获取所有医院信息
export function listAllHospital(data) {
    return requestV1.postJson(prefix + '/hospitalMap/listAllHospital', data);
}
// 医院下的科室---根据主键查询医院下的科室
export function crawlershospitaldeptQuery(data) {
    return requestV1.postForm(dmfix + '/crawlershospitaldept/hospital/dept', data);
}
// 通过医院主键和科室主键查询其下医生
export function crawlershospitaldoctor(data){
    return requestV1.postForm(dmfix + `/crawlershospitaldoctor/hospital/dept/doctors`, data);
}