/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'
import env from "@/config/env";
// excel导入数据
const uploadExcel = env.ctx + prefix + '/balanceApply/v1/uploadexcel'
export { uploadExcel }

//手动给指定的帐户平帐
export function addApply(data) {
  return requestV1.postJson(prefix + '/balanceApply/addApply', data);
}

//查询帐户收支列表
export function queryPage(data) {
  return requestV1.postJson(prefix + '/balanceApply/queryPage', data);
}

// 审核平账
export function audit(data) {
  return requestV1.postJson(prefix + '/balanceApply/audit', data);
}
