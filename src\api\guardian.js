/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

//编辑维护人员
export function editGuardian(data) {
  return requestV1.putJson(prefix + '/guardian/editGuardian', data);
}

//查询所有所有未绑定维护人员的用户
export function listAllNoBindUser(data) {
  return requestV1.get(prefix + '/guardian/listAllNoBindUser', data);
}

//维护人员批量解绑设备
export function batchUnBindDevice(data) {
  return requestV1.postJson(prefix + '/guardian/batchUnBindDevice', data);
}

//维护人员渠道商批量绑定设备
export function batchBindDevice(data) {
  return requestV1.postJson(prefix + '/guardian/batchBindDevice', data);
}

//维护人员设备分页数据
export function queryDevicePage(data) {

  return requestV1.postJson(prefix + '/guardian/queryDevicePage', data);
}

//添加维护人员
export function addGuardian(data) {
   return requestV1.postJson(prefix + '/guardian/addGuardian', data);
}

//维护人员设备分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/guardian/queryPage', data);
}

//查询所有维护人员数据
export function queryList(data) {
    return requestV1.postJson(prefix + '/guardian/queryList', data);
}

// 批量删除
export function deletes(data) {
  return requestV1.postJson(prefix + '/guardian/deletes', data);
}
