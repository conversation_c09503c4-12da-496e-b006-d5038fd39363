/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api'

//新增医院禁塑与未禁塑占比分布
export function getHospitalProhibitRate(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalProhibitRate', data);


}

//新增医院禁塑与未禁塑数据情况
export function getHospitalProhibitSituation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalProhibitSituation', data);


}

//医院机器台数投放情况
export function getHospitalDeviceSituation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalDeviceSituation', data);


}

//新增医院机器投放情况
export function getMouthNewDeviceSituation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getMouthNewDeviceSituation', data);


}

//新增医院等级占比情况
export function getHospitalLevelProportionSituationList(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalLevelProportionSituationList', data);


}

//机器运营情况
export function getStopHospital(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getStopHospital', data);


}

//机器运营情况
export function getExitOperation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getExitOperation', data);


}

//机器运营情况
export function getHospitalOperation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalOperation', data);


}

//省代-市代开拓医院情况-共用一个接口
export function getProvinceCityExploitPlanSituationList(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getProvinceCityExploitPlanSituationList', data);


}

//医院开拓情况进度数组-上面数据
export function getHospitalExploitPlanSituationList(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalExploitPlanSituationList', data);


}

//医院开拓情况进度数据-下面数据
export function getHospitalExploitPlanSituationData(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalExploitPlanSituationData', data);


}

//医院上线情况-累计上线医院数
export function getHospitalOnLineSituationForTotal(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalOnLineSituationForTotal', data);


}

//医院上线情况-新增上线医院数
export function getHospitalOnLineSituationForNew(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalOnLineSituationForNew', data);


}

//医院等级占比情况
export function getHospitalLevelSituation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalLevelSituation', data);


}

//医院开拓情况
export function getHospitalExploitSituation(data) {
    return requestV1.postJson(prefix + '/hospitalExploitReport/getHospitalExploitSituation', data);


}
